<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'default' 表示显示默认导航栏
    "navigationStyle": "default",
    "navigationBarTitleText": "首页"
  }
}
</route>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'Home',
})

const userStore = useUserStore()

// 导航到搜索页面
function navigateToSearch() {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

// 证件照类型导航
function indexGoNextPage(type: number) {
  console.log('选择证件照类型:', type)
  // 这里可以添加导航逻辑，根据type参数跳转到不同页面
  // type 0: 一寸照片
  // type 1: 二寸照片
}

// 导航到换背景页面
function navigateToCutout() {
  uni.navigateTo({
    url: '/pages/cutout/index',
  })
}

// 测试 uni API 自动引入
onLoad(() => {
  console.log('证件照首页加载完成')
})

console.log('index')
</script>

<template>
  <view class="bg-white">
    <!-- 搜索框 -->
    <view style="margin: 0rpx 20rpx;">
      <wd-search
        placeholder="搜索证件照名称、尺寸"
        placeholder-left
        hide-cancel
        disabled
        @click="navigateToSearch"
      />
    </view>

    <!-- 图片布局 -->
    <view style="display: flex; width: 100%; height: 370rpx; margin: 15rpx 0rpx 30rpx 0rpx;">
      <view style="margin-left: 35rpx;" @click="indexGoNextPage(0)">
        <image src="/static/icon/one-inch.png" style="width: 328rpx; height: 370rpx;" />
      </view>
      <view style="width: 50%; display: flex; flex-direction: column; margin-left: 26rpx;">
        <view style="width: 332rpx; height: 172rpx;" @click="indexGoNextPage(1)">
          <image src="/static/icon/two-inch.png" style="width: 332rpx; height: 172rpx;" />
        </view>
        <view style="width: 332rpx; height: 172rpx; margin-top: 29rpx;" @click="navigateToCutout">
          <image src="/static/icon/change-bg.png" style="width: 332rpx; height: 172rpx;" />
        </view>
      </view>
    </view>

    <!-- 用户信息显示 -->
    <view class="mt-4 text-center">
      <text class="text-xs text-gray-500">
        当前用户信息：{{ userStore.userInfo.token ? '已登录' : '未登录' }}
      </text>
    </view>
  </view>
</template>
